// Main application JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the app
    initializeApp();
});

function initializeApp() {
    // Load orders from the database
    loadOrders();

    // Setup event listeners
    setupEventListeners();

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize pagination
    setupPagination();
}

function setupEventListeners() {
    // Add order form submission
    document.getElementById('saveOrderBtn').addEventListener('click', saveOrder);

    // Change stage form submission
    document.getElementById('changeStageForm').addEventListener('submit', changeOrderStage);

    // Filter form submission
    document.getElementById('filterForm').addEventListener('submit', applyFilters);

    // Select all checkbox
    document.getElementById('selectAll').addEventListener('change', toggleSelectAll);

    // Bulk action buttons
    document.getElementById('bulkMoveToInvestigation').addEventListener('click', () => bulkChangeStage('investigation'));
    document.getElementById('bulkMoveToAudit').addEventListener('click', () => bulkChangeStage('audit'));
    document.getElementById('bulkMoveToEntry').addEventListener('click', () => bulkChangeStage('entry'));
}

// Load orders from the database and populate the table
async function loadOrders() {
    try {
        // In a real app, this would be an API call to the backend
        // For now, we'll use mock data
        const orders = await getMockOrders();

        // Set up pagination
        const itemsPerPage = 5;
        const paginatedOrders = orders.slice(0, itemsPerPage);

        populateOrdersTable(paginatedOrders);
        updateStatsCards(orders);
        updatePaginationUI(1, Math.ceil(orders.length / itemsPerPage));
    } catch (error) {
        console.error('Error loading orders:', error);
        showToast('حدث خطأ أثناء تحميل البيانات', 'danger');
    }
}

// Update statistics cards with order counts
function updateStatsCards(orders) {
    // Count orders by stage
    const investigationCount = orders.filter(order => order.stage === 'investigation').length;
    const auditCount = orders.filter(order => order.stage === 'audit').length;
    const entryCount = orders.filter(order => order.stage === 'entry').length;
    const totalCount = orders.length;

    // Update the DOM with animation
    animateValue('investigationCount', 0, investigationCount, 1000);
    animateValue('auditCount', 0, auditCount, 1000);
    animateValue('entryCount', 0, entryCount, 1000);
    animateValue('totalCount', 0, totalCount, 1000);
}

// Animate numeric values
function animateValue(id, start, end, duration) {
    const obj = document.getElementById(id);
    const range = end - start;
    const minTimer = 50;
    let stepTime = Math.abs(Math.floor(duration / range));
    stepTime = Math.max(stepTime, minTimer);
    const startTime = new Date().getTime();
    const endTime = startTime + duration;
    let timer;

    function run() {
        const now = new Date().getTime();
        const remaining = Math.max((endTime - now) / duration, 0);
        const value = Math.round(end - (remaining * range));
        obj.innerHTML = value;
        if (value == end) {
            clearInterval(timer);
        }
    }

    timer = setInterval(run, stepTime);
    run();
}

// Generate mock orders for demonstration
function getMockOrders() {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve([
                {
                    id: 1,
                    number: 'ORD-2023-001',
                    details: 'صرف مستحقات الموظفين للشهر الأول',
                    stage: 'investigation',
                    lastAction: 'تم إنشاء الأمر',
                    lastActionDate: '2023-10-01'
                },
                {
                    id: 2,
                    number: 'ORD-2023-002',
                    details: 'صرف مستلزمات قسم تكنولوجيا المعلومات',
                    stage: 'audit',
                    lastAction: 'تم تحويل الأمر إلى التدقيق',
                    lastActionDate: '2023-10-02'
                },
                {
                    id: 3,
                    number: 'ORD-2023-003',
                    details: 'صرف مكافآت نهاية العام',
                    stage: 'entry',
                    lastAction: 'تم تحويل الأمر إلى الإدخال',
                    lastActionDate: '2023-10-03'
                },
                {
                    id: 4,
                    number: 'ORD-2023-004',
                    details: 'صرف مستحقات العاملين بالساعة',
                    stage: 'investigation',
                    lastAction: 'تم إنشاء الأمر',
                    lastActionDate: '2023-10-04'
                },
                {
                    id: 5,
                    number: 'ORD-2023-005',
                    details: 'صرف بدلات انتداب',
                    stage: 'audit',
                    lastAction: 'تم تحويل الأمر إلى التدقيق',
                    lastActionDate: '2023-10-05'
                }
            ]);
        }, 500);
    });
}

// Populate the orders table with data
function populateOrdersTable(orders) {
    const tableBody = document.getElementById('ordersTableBody');
    tableBody.innerHTML = '';

    if (orders.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `
            <td colspan="6" class="text-center py-4">
                <i class="fas fa-inbox fa-2x text-muted mb-2 d-block"></i>
                <span class="text-muted">لا توجد أوامر صرف</span>
            </td>
        `;
        tableBody.appendChild(emptyRow);
        return;
    }

    orders.forEach(order => {
        const row = document.createElement('tr');
        row.dataset.orderId = order.id;

        const stageBadgeClass = `badge-${order.stage}`;
        const stageText = getStageText(order.stage);

        row.innerHTML = `
            <td>
                <div class="form-check">
                    <input class="form-check-input order-checkbox" type="checkbox" value="${order.id}">
                </div>
            </td>
            <td>${order.number}</td>
            <td>${order.details}</td>
            <td><span class="badge ${stageBadgeClass}">${stageText}</span></td>
            <td>
                <div>${order.lastAction}</div>
                <small class="text-muted">${formatDate(order.lastActionDate)}</small>
            </td>
            <td class="text-center">
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="openChangeStageModal(${order.id})" title="تغيير المرحلة">
                        <i class="fas fa-exchange-alt"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="viewOrderDetails(${order.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // Setup checkbox change listeners
    document.querySelectorAll('.order-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
}

// Get the Arabic text for a stage
function getStageText(stage) {
    const stages = {
        'investigation': 'التحقيق (المحاسبة)',
        'audit': 'التدقيق (قسم التدقيق)',
        'entry': 'الإدخال (قسم التأدية والرواتب)'
    };

    return stages[stage] || stage;
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Save a new order
async function saveOrder() {
    const orderNumber = document.getElementById('orderNumber').value.trim();
    const orderDetails = document.getElementById('orderDetails').value.trim();
    const orderStage = document.getElementById('orderStage').value;

    if (!orderNumber || !orderDetails) {
        showToast('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    try {
        // Show loading state
        const saveBtn = document.getElementById('saveOrderBtn');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> جاري الحفظ...';
        saveBtn.disabled = true;

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // In a real app, this would be an API call to save to the database
        const newOrder = {
            id: Date.now(), // Mock ID
            number: orderNumber,
            details: orderDetails,
            stage: orderStage,
            lastAction: 'تم إنشاء الأمر',
            lastActionDate: new Date().toISOString().split('T')[0]
        };

        // Show success message
        showToast('تم حفظ أمر الصرف بنجاح', 'success');

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addOrderModal'));
        modal.hide();

        // Reset the form
        document.getElementById('addOrderForm').reset();

        // Reload orders
        loadOrders();

        // Reset button state
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    } catch (error) {
        console.error('Error saving order:', error);
        showToast('حدث خطأ أثناء حفظ الأمر', 'danger');

        // Reset button state
        const saveBtn = document.getElementById('saveOrderBtn');
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    }
}

// Open the change stage modal
function openChangeStageModal(orderId) {
    document.getElementById('changeOrderId').value = orderId;

    const modal = new bootstrap.Modal(document.getElementById('changeStageModal'));
    modal.show();
}

// Change the stage of an order
async function changeOrderStage(event) {
    event.preventDefault();

    const orderId = document.getElementById('changeOrderId').value;
    const newStage = document.getElementById('newStage').value;
    const changeNote = document.getElementById('changeNote').value.trim();

    try {
        // Show loading state
        const submitBtn = document.querySelector('#changeStageForm button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> جاري التغيير...';
        submitBtn.disabled = true;

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // In a real app, this would be an API call to update the database
        console.log(`Changing order ${orderId} to stage ${newStage} with note: ${changeNote}`);

        // Show success message
        showToast(`تم تغيير مرحلة الأمر إلى ${getStageText(newStage)} بنجاح`, 'success');

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('changeStageModal'));
        modal.hide();

        // Reset the form
        document.getElementById('changeStageForm').reset();

        // Reload orders
        loadOrders();

        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    } catch (error) {
        console.error('Error changing order stage:', error);
        showToast('حدث خطأ أثناء تغيير مرحلة الأمر', 'danger');

        // Reset button state
        const submitBtn = document.querySelector('#changeStageForm button[type="submit"]');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

// Toggle select all checkboxes
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll').checked;
    const checkboxes = document.querySelectorAll('.order-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll;
    });

    updateSelectedCount();
}

// Update the count of selected orders and enable/disable bulk actions
function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.order-checkbox:checked');
    const count = selectedCheckboxes.length;

    document.getElementById('selectedCount').textContent = `${count} محدد`;

    // Enable/disable bulk action buttons
    const bulkButtons = [
        document.getElementById('bulkMoveToInvestigation'),
        document.getElementById('bulkMoveToAudit'),
        document.getElementById('bulkMoveToEntry')
    ];

    bulkButtons.forEach(button => {
        button.disabled = count === 0;
    });
}

// Change stage for multiple orders at once
async function bulkChangeStage(newStage) {
    const selectedCheckboxes = document.querySelectorAll('.order-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (selectedIds.length === 0) {
        showToast('يرجى تحديد أوامر صرف أولاً', 'warning');
        return;
    }

    try {
        // Disable buttons during operation
        const bulkButtons = [
            document.getElementById('bulkMoveToInvestigation'),
            document.getElementById('bulkMoveToAudit'),
            document.getElementById('bulkMoveToEntry')
        ];

        const originalTexts = bulkButtons.map(btn => btn.innerHTML);
        bulkButtons.forEach(btn => {
            btn.disabled = true;
            if (btn.id.includes(newStage)) {
                btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> جاري التحويل...';
            }
        });

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500));

        // In a real app, this would be an API call to update multiple records
        console.log(`Bulk changing orders ${selectedIds.join(', ')} to stage ${newStage}`);

        // Show success message
        showToast(`تم تحويل ${selectedIds.length} أوامر إلى ${getStageText(newStage)} بنجاح`, 'success');

        // Reload orders with pagination
        const currentPage = parseInt(document.querySelector('.pagination .page-item.active .page-link').textContent) || 1;
        await navigateToPage(currentPage);

        // Reset button states
        bulkButtons.forEach((btn, index) => {
            btn.disabled = false;
            btn.innerHTML = originalTexts[index];
        });

        // Uncheck all checkboxes
        document.getElementById('selectAll').checked = false;
        document.querySelectorAll('.order-checkbox:checked').forEach(cb => {
            cb.checked = false;
        });
        updateSelectedCount();
    } catch (error) {
        console.error('Error in bulk stage change:', error);
        showToast('حدث خطأ أثناء تحويل الأوامر', 'danger');

        // Reset button states
        const bulkButtons = [
            document.getElementById('bulkMoveToInvestigation'),
            document.getElementById('bulkMoveToAudit'),
            document.getElementById('bulkMoveToEntry')
        ];
        bulkButtons.forEach((btn, index) => {
            btn.disabled = false;
            btn.innerHTML = originalTexts[index];
        });
    }
}

// Setup pagination
function setupPagination() {
    // Add event listeners to pagination links
    document.querySelectorAll('.pagination .page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.getAttribute('data-page') || this.textContent;
            if (page === 'السابق' || page === 'التالي') {
                navigatePage(page);
            } else {
                navigateToPage(parseInt(page));
            }
        });
    });
}

// Navigate to a specific page
async function navigateToPage(pageNum) {
    try {
        // Show loading state
        showToast('جاري تحميل الصفحة...', 'info');

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));

        // Get orders for the requested page
        const orders = await getMockOrders();
        const itemsPerPage = 5;
        const startIndex = (pageNum - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedOrders = orders.slice(startIndex, endIndex);

        // Update the table
        populateOrdersTable(paginatedOrders);

        // Update pagination UI
        updatePaginationUI(pageNum, Math.ceil(orders.length / itemsPerPage));

        // Scroll to top of table
        document.querySelector('.card').scrollIntoView({ behavior: 'smooth' });
    } catch (error) {
        console.error('Error navigating to page:', error);
        showToast('حدث خطأ أثناء تحميل الصفحة', 'danger');
    }
}

// Navigate to previous or next page
function navigatePage(direction) {
    const currentPage = parseInt(document.querySelector('.pagination .page-item.active .page-link').textContent);
    if (direction === 'السابق' && currentPage > 1) {
        navigateToPage(currentPage - 1);
    } else if (direction === 'التالي') {
        const totalPages = document.querySelectorAll('.pagination .page-link').length - 2; // Subtract prev and next
        if (currentPage < totalPages) {
            navigateToPage(currentPage + 1);
        }
    }
}

// Update pagination UI
function updatePaginationUI(currentPage, totalPages) {
    const pagination = document.querySelector('.pagination');
    pagination.innerHTML = '';

    // Previous button
    const prevDisabled = currentPage === 1 ? 'disabled' : '';
    pagination.innerHTML += `
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" data-page="prev">السابق</a>
        </li>
    `;

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const active = i === currentPage ? 'active' : '';
        pagination.innerHTML += `
            <li class="page-item ${active}">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
        `;
    }

    // Next button
    const nextDisabled = currentPage === totalPages ? 'disabled' : '';
    pagination.innerHTML += `
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" data-page="next">التالي</a>
        </li>
    `;

    // Re-attach event listeners
    setupPagination();
}

// Apply filters to the orders table
async function applyFilters(event) {
    event.preventDefault();

    const orderNumber = document.getElementById('orderNumberSearch').value.trim();
    const stage = document.getElementById('stageFilter').value;

    try {
        // Show loading state
        const submitBtn = event.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> جاري البحث...';
        submitBtn.disabled = true;

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 800));

        // In a real app, this would be an API call with filter parameters
        console.log(`Applying filters: orderNumber=${orderNumber}, stage=${stage}`);

        // Get filtered orders
        const orders = await getMockOrders();
        let filteredOrders = orders;

        // Apply filters
        if (orderNumber) {
            filteredOrders = filteredOrders.filter(order => 
                order.number.toLowerCase().includes(orderNumber.toLowerCase())
            );
        }

        if (stage) {
            filteredOrders = filteredOrders.filter(order => order.stage === stage);
        }

        // Update the table with pagination
        const itemsPerPage = 5;
        const paginatedOrders = filteredOrders.slice(0, itemsPerPage);
        populateOrdersTable(paginatedOrders);
        updateStatsCards(filteredOrders);
        updatePaginationUI(1, Math.ceil(filteredOrders.length / itemsPerPage));

        // Show a message about the applied filters
        let filterMessage = 'تم تطبيق الفلتر بنجاح';
        if (orderNumber) filterMessage += ` (رقم الأمر: ${orderNumber})`;
        if (stage) filterMessage += ` (المرحلة: ${getStageText(stage)})`;
        if (!orderNumber && !stage) filterMessage = 'تم عرض جميع الأوامر';

        showToast(filterMessage, 'info');

        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    } catch (error) {
        console.error('Error applying filters:', error);
        showToast('حدث خطأ أثناء تطبيق الفلتر', 'danger');

        // Reset button state
        const submitBtn = event.target.querySelector('button[type="submit"]');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

// View order details (placeholder function)
function viewOrderDetails(orderId) {
    // In a real app, this would open a detailed view or navigate to a details page
    showToast(`عرض تفاصيل الأمر رقم ${orderId}`, 'info');
    console.log(`Viewing details for order ${orderId}`);
}

// Show a toast notification
function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastId = `toast-${Date.now()}`;
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    // Initialize and show the toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });

    bsToast.show();

    // Remove the toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
