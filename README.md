# نظام تتبع مسار أوامر الصرف

## وصف المشروع
نظام ويب لإدارة وتتبع مسار أوامر الصرف، يستخدم تقنيات HTML و CSS و JavaScript و Bootstrap للواجهة الأمامية، و Python مع Flask و SQLite للواجهة الخلفية وقاعدة البيانات.

## المميزات
- عرض جميع أوامر الصرف في جدول منظم
- تتبع مراحل الأمر: التحقيق (المحاسبة)، التدقيق (قسم التدقيق)، الإدخال (قسم التأدية والرواتب)
- إضافة أوامر صرف جديدة
- تغيير مسار الأوامر يدويًا أو تلقائيًا حسب الترتيب
- اختيار معاملة واحدة أو مجموعة معاملات وتغيير مسارها
- فلترة وبحث حسب رقم أمر الصرف أو المرحلة الحالية
- تسجيل آخر عملية تمت على كل معاملة
- تصميم عربي أنيق باستخدام Bootstrap

## هيكل المشروع
```
تتبع مسار الاوامر/
├── app.py                 # ملف تطبيق Flask الخلفي
├── database.py            # وحدة قاعدة البيانات SQLite
├── requirements.txt       # المكتبات المطلوبة
├── index.html             # الصفحة الرئيسية
├── css/
│   └── style.css          # تنسيقات CSS مخصصة
└── js/
    ├── app.js             # كود JavaScript الرئيسي
    └── utils.js           # وظائف JavaScript المساعدة
```

## المتطلبات
- Python 3.7+
- المكتبات المذكورة في ملف requirements.txt

## التثبيت والتشغيل
1. تأكد من تثبيت Python على جهازك
2. قم بتثبيت المكتبات المطلوبة:
   ```
   pip install -r requirements.txt
   ```
3. قم بتشغيل تطبيق Flask:
   ```
   python app.py
   ```
4. افتح المتصفح وانتقل إلى العنوان:
   ```
   http://localhost:5000
   ```

## استخدام النظام
1. **إضافة أمر صرف جديد**: انقر على زر "إضافة أمر صرف جديد" واملأ الحقول المطلوبة
2. **عرض الأوامر**: يتم عرض جميع الأوامر في الجدول الرئيسي مع معلومات عن المرحلة الحالية وآخر عملية
3. **تغيير مرحلة أمر**: انقر على زر التغيير في عمود الإجراءات، أو حدد عدة أوامر واستخدم أزرار التغيير الجماعي
4. **التصفية والبحث**: استخدم نموذج البحث والتصفية في الأعلى للبحث عن أوامر محددة
5. **عرض السجل**: يتم عرض آخر عملية تمت على كل أمر في عمود "آخر عملية"

## قاعدة البيانات
يستخدم النظام قاعدة بيانات SQLite لتخزين المعلومات، وتتكون من جدولين رئيسيين:
- `orders`: لتخزين معلومات أوامر الصرف
- `order_logs`: لتسجيل تاريخ التغييرات على كل أمر

## المساهمة
يمكنك المساهمة في تطوير النظام عن طريق:
- الإبلاغ عن المشاكل والأخطاء
- اقتراح تحسينات ومميزات جديدة
- المساهمة في الكود المصدري

## الرخصة
جميع الحقوق محفوظة.
