// Utility functions for the Order Tracking System

// Show a toast notification
function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = '1055';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    // Initialize and show the toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });
    bsToast.show();

    // Remove the toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// View order details
async function viewOrderDetails(orderId) {
    try {
        // Show loading state
        showToast('جاري تحميل التفاصيل...', 'info');

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 800));

        // Get order details
        const orders = await getMockOrders();
        const order = orders.find(o => o.id == orderId);

        if (!order) {
            showToast('الأمر غير موجود', 'danger');
            return;
        }

        // Create details modal dynamically
        const modalId = 'orderDetailsModal';

        // Remove existing modal if any
        const existingModal = document.getElementById(modalId);
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${modalId}Label">تفاصيل أمر الصرف</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6>رقم الأمر</h6>
                                    <p>${order.number}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>المرحلة الحالية</h6>
                                    <p><span class="badge badge-${order.stage}">${getStageText(order.stage)}</span></p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <h6>التفاصيل</h6>
                                    <p>${order.details}</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>تاريخ الإنشاء</h6>
                                    <p>${formatDate(order.created_at)}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>آخر تحديث</h6>
                                    <p>${formatDate(order.updated_at)}</p>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h6>سجل التغييرات</h6>
                                <div class="timeline">
                                    <div class="timeline-item">
                                        <div class="timeline-badge bg-info"><i class="fas fa-plus"></i></div>
                                        <div class="timeline-card">
                                            <div class="timeline-card-header">
                                                <span class="timeline-date">${formatDate(order.created_at)}</span>
                                                <h6 class="timeline-title">إنشاء الأمر</h6>
                                            </div>
                                            <div class="timeline-card-body">
                                                <p>تم إنشاء أمر الصرف في النظام</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-badge bg-warning"><i class="fas fa-exchange-alt"></i></div>
                                        <div class="timeline-card">
                                            <div class="timeline-card-header">
                                                <span class="timeline-date">${formatDate(order.updated_at)}</span>
                                                <h6 class="timeline-title">تغيير المرحلة</h6>
                                            </div>
                                            <div class="timeline-card-body">
                                                <p>${order.lastAction}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="openChangeStageModal(${order.id})">
                                <i class="fas fa-exchange-alt me-1"></i> تغيير المرحلة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Add timeline styles if not already added
        if (!document.getElementById('timeline-styles')) {
            const timelineStyles = `
                <style id="timeline-styles">
                    .timeline {
                        position: relative;
                        margin: 0 0 0 30px;
                        padding: 0;
                        list-style: none;
                    }

                    .timeline:before {
                        content: '';
                        position: absolute;
                        top: 0;
                        bottom: 0;
                        width: 2px;
                        background: #eee;
                        left: 0;
                        margin-left: -1px;
                    }

                    .timeline-item {
                        position: relative;
                        margin-bottom: 20px;
                    }

                    .timeline-badge {
                        width: 36px;
                        height: 36px;
                        line-height: 36px;
                        font-size: 1.2rem;
                        text-align: center;
                        position: absolute;
                        left: -50px;
                        top: 0;
                        border-radius: 50%;
                        color: white;
                    }

                    .timeline-card {
                        background: #fff;
                        border-radius: 8px;
                        padding: 15px;
                        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    }

                    .timeline-card-header {
                        border-bottom: 1px solid #eee;
                        padding-bottom: 10px;
                        margin-bottom: 10px;
                    }

                    .timeline-date {
                        font-size: 0.85rem;
                        color: #777;
                    }

                    .timeline-title {
                        margin: 0;
                        font-size: 1rem;
                        font-weight: 600;
                    }

                    .timeline-card-body p {
                        margin: 0;
                    }
                </style>
            `;
            document.head.insertAdjacentHTML('beforeend', timelineStyles);
        }

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    } catch (error) {
        console.error('Error viewing order details:', error);
        showToast('حدث خطأ أثناء عرض التفاصيل', 'danger');
    }
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Get the Arabic text for a stage
function getStageText(stage) {
    const stages = {
        'investigation': 'التحقيق (المحاسبة)',
        'audit': 'التدقيق (قسم التدقيق)',
        'entry': 'الإدخال (قسم التأدية والرواتب)'
    };

    return stages[stage] || stage;
}

// Get the next stage in the workflow
function getNextStage(currentStage) {
    const stages = ['investigation', 'audit', 'entry'];
    const currentIndex = stages.indexOf(currentStage);

    if (currentIndex === -1 || currentIndex === stages.length - 1) {
        return currentStage; // Return current stage if not found or already at the end
    }

    return stages[currentIndex + 1];
}

// Validate order number format
function validateOrderNumber(orderNumber) {
    // Basic validation - in a real app, this would be more specific to business requirements
    return orderNumber && orderNumber.trim().length > 0;
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}
