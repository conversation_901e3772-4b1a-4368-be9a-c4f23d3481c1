/* Custom CSS for the Order Tracking System - Military Financial Style */

:root {
    --primary-color: #0a3d62;
    --secondary-color: #3c6382;
    --success-color: #009432;
    --info-color: #0652DD;
    --warning-color: #f39c12;
    --danger-color: #c0392b;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --saudi-green: #006F34;
    --saudi-white: #FFFFFF;
    --military-gold: #d4af37;
    --military-dark: #1a1a2e;
    --military-blue: #16213e;
    --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

/* Header Styles */
.header-top {
    background: linear-gradient(90deg, var(--military-dark) 0%, var(--military-blue) 100%);
    color: var(--saudi-white);
    padding: 15px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.header-top::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--military-gold) 0%, #f1c40f 100%);
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    position: relative;
    z-index: 2;
}

.logo-container img {
    height: 80px;
    margin-left: 20px;
    border-radius: 50%;
    border: 2px solid var(--military-gold);
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.5);
}

.ministry-title {
    font-size: 2.2rem;
    font-weight: bold;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
}

.system-title {
    font-size: 1.6rem;
    margin: 5px 0 0;
    opacity: 0.9;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Stage badges styling */
.badge-investigation {
    background-color: var(--info-color);
    color: var(--dark-color);
}

.badge-audit {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.badge-entry {
    background-color: var(--success-color);
}

/* Table styling */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table-hover tbody tr:hover {
    background-color: rgba(10, 61, 98, 0.08);
    cursor: pointer;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--military-dark);
    background-color: rgba(10, 61, 98, 0.05);
    padding: 15px;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table td {
    padding: 15px;
    vertical-align: middle;
}

/* Card styling */
.card {
    border: none;
    box-shadow: var(--card-shadow);
    border-radius: 12px;
    margin-bottom: 25px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(90deg, var(--military-dark) 0%, var(--military-blue) 100%);
    color: var(--saudi-white);
    border-bottom: none;
    padding: 15px 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-header h5 {
    margin: 0;
    font-size: 1.1rem;
}

.card-body {
    padding: 25px;
}

/* Button styling */
.btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.btn-group .btn {
    border-radius: 8px;
    margin-right: 8px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.btn-primary {
    background: linear-gradient(90deg, var(--military-dark) 0%, var(--military-blue) 100%);
    border: none;
    color: var(--saudi-white);
}

.btn-primary:hover, .btn-primary:focus {
    background: linear-gradient(90deg, var(--military-blue) 0%, var(--primary-color) 100%);
    border: none;
    color: var(--saudi-white);
}

.btn-outline-primary {
    border: 2px solid var(--military-dark);
    color: var(--military-dark);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--military-dark);
    border-color: var(--military-dark);
    color: var(--saudi-white);
}

.btn-success {
    background: linear-gradient(90deg, var(--success-color) 0%, #00b894 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(90deg, var(--warning-color) 0%, #fdcb6e 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(90deg, var(--danger-color) 0%, #e84393 100%);
    border: none;
}

/* Modal styling */
.modal-header {
    background-color: var(--light-color);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.modal-footer {
    background-color: var(--light-color);
    border-top: 1px solid rgba(0, 0, 0, 0.125);
}

/* Form styling */
.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Checkbox styling */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Pagination styling */
.pagination .page-link {
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Banners styling */
.banner-container {
    margin-bottom: 30px;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    position: relative;
}

.banner-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.7) 0%, rgba(22, 33, 62, 0.7) 100%);
    z-index: 1;
}

.banner-slide {
    height: 250px;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
    z-index: 2;
}

.banner-slide .text-center {
    padding: 20px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(5px);
    max-width: 80%;
}

.banner-slide h3 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-slide p {
    font-size: 1.2rem;
    margin: 0;
    opacity: 0.9;
}

.carousel-control-prev,
.carousel-control-next {
    width: 50px;
    height: 50px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.7;
    transition: all 0.3s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background-color: rgba(255, 255, 255, 0.3);
    opacity: 1;
}

.carousel-control-prev {
    right: 20px;
    left: auto;
}

.carousel-control-next {
    left: 20px;
    right: auto;
}

.carousel-indicators {
    bottom: 20px;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
}

.carousel-indicators button.active {
    background-color: var(--military-gold);
}

/* Stats cards */
.stats-card {
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    color: white;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.stats-card::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stats-card.investigation {
    background: linear-gradient(135deg, var(--info-color) 0%, #3498db 100%);
}

.stats-card.audit {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f39c12 100%);
}

.stats-card.entry {
    background: linear-gradient(135deg, var(--success-color) 0%, #27ae60 100%);
}

.stats-card.total {
    background: linear-gradient(135deg, var(--military-dark) 0%, var(--military-blue) 100%);
}

.stats-card h3 {
    font-size: 2.5rem;
    margin: 10px 0;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.stats-card p {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.stats-card i {
    font-size: 2.5rem;
    opacity: 0.8;
    position: relative;
    z-index: 1;
}

/* Footer styling */
footer {
    background: linear-gradient(90deg, var(--military-dark) 0%, var(--military-blue) 100%);
    color: var(--saudi-white);
    margin-top: 40px;
    padding: 30px 0;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--military-gold) 0%, #f1c40f 100%);
}

footer .container {
    position: relative;
    z-index: 1;
}

footer p {
    margin: 0;
    font-size: 1rem;
    opacity: 0.9;
}

/* Form styling */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-control:focus, .form-select:focus {
    border-color: var(--military-blue);
    box-shadow: 0 0 0 0.25rem rgba(22, 33, 62, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--military-dark);
}

/* Modal styling */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(90deg, var(--military-dark) 0%, var(--military-blue) 100%);
    color: var(--saudi-white);
    border-bottom: none;
    padding: 20px 25px;
}

.modal-title {
    font-weight: 600;
    font-size: 1.3rem;
}

.modal-header .btn-close {
    filter: brightness(0) invert(1);
    opacity: 0.8;
    transition: all 0.3s ease;
}

.modal-header .btn-close:hover {
    opacity: 1;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
    padding: 20px 25px;
}

/* Badge styling */
.badge {
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.85rem;
}

/* Checkbox styling */
.form-check-input {
    width: 1.2em;
    height: 1.2em;
    margin-top: 0.3em;
    border-radius: 0.35em;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.form-check-input:checked {
    background-color: var(--military-blue);
    border-color: var(--military-blue);
}

/* Pagination styling */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    color: var(--military-dark);
    border-radius: 8px;
    margin: 0 3px;
    border: none;
    padding: 10px 15px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: rgba(22, 33, 62, 0.1);
    color: var(--military-blue);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(90deg, var(--military-dark) 0%, var(--military-blue) 100%);
    border-color: transparent;
    color: var(--saudi-white);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
    }

    .d-flex.justify-content-between > div {
        margin-bottom: 1rem;
    }

    .btn-group {
        width: 100%;
    }

    .btn-group .btn {
        flex: 1;
        margin-right: 5px;
    }

    .ministry-title {
        font-size: 1.8rem;
    }

    .system-title {
        font-size: 1.3rem;
    }

    .stats-card h3 {
        font-size: 2rem;
    }

    .stats-card p {
        font-size: 1rem;
    }

    .banner-slide h3 {
        font-size: 1.8rem;
    }

    .banner-slide p {
        font-size: 1rem;
    }

    .card-body {
        padding: 15px;
    }

    .table th, .table td {
        padding: 10px;
        font-size: 0.9rem;
    }
}
