# Flask backend for the Order Tracking System

from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import json
from database import db

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Serve the main page
@app.route('/')
def index():
    return render_template('index.html')

# API Routes

# Get all orders with optional filters
@app.route('/api/orders', methods=['GET'])
def get_orders():
    stage = request.args.get('stage')
    number_search = request.args.get('number_search')

    orders = db.get_orders(stage=stage, number_search=number_search)

    # Add latest log to each order
    for order in orders:
        latest_log = db.get_latest_log(order['id'])
        if latest_log:
            order['lastAction'] = latest_log['action']
            order['lastActionDate'] = latest_log['created_at']
        else:
            order['lastAction'] = 'لا توجد سجلات'
            order['lastActionDate'] = order['created_at']

    return jsonify(orders)

# Get a single order by ID
@app.route('/api/orders/<int:order_id>', methods=['GET'])
def get_order(order_id):
    order = db.get_order(order_id)

    if not order:
        return jsonify({'error': 'Order not found'}), 404

    # Get order logs
    logs = db.get_order_logs(order_id)

    return jsonify({
        'order': order,
        'logs': logs
    })

# Create a new order
@app.route('/api/orders', methods=['POST'])
def create_order():
    data = request.get_json()

    if not data or 'number' not in data or 'details' not in data:
        return jsonify({'error': 'Missing required fields'}), 400

    try:
        order_id = db.add_order(
            number=data['number'],
            details=data['details'],
            stage=data.get('stage', 'investigation')
        )

        order = db.get_order(order_id)
        return jsonify(order), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update order stage
@app.route('/api/orders/<int:order_id>/stage', methods=['PUT'])
def update_order_stage(order_id):
    data = request.get_json()

    if not data or 'new_stage' not in data:
        return jsonify({'error': 'Missing new_stage field'}), 400

    try:
        success = db.update_order_stage(
            order_id=order_id,
            new_stage=data['new_stage'],
            notes=data.get('notes')
        )

        if not success:
            return jsonify({'error': 'Order not found'}), 404

        order = db.get_order(order_id)
        return jsonify(order)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Bulk update stage for multiple orders
@app.route('/api/orders/bulk/stage', methods=['PUT'])
def bulk_update_stage():
    data = request.get_json()

    if not data or 'order_ids' not in data or 'new_stage' not in data:
        return jsonify({'error': 'Missing required fields'}), 400

    try:
        updated_count = db.bulk_update_stage(
            order_ids=data['order_ids'],
            new_stage=data['new_stage'],
            notes=data.get('notes')
        )

        return jsonify({'updated_count': updated_count})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Get order logs
@app.route('/api/orders/<int:order_id>/logs', methods=['GET'])
def get_order_logs(order_id):
    logs = db.get_order_logs(order_id)
    return jsonify(logs)

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    app.run(debug=True)
