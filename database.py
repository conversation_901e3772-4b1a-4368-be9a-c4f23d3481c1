# SQLite database module for the Order Tracking System

import sqlite3
import datetime
from typing import List, Dict, Optional

class OrderDatabase:
    """Class to handle SQLite database operations for the order tracking system."""

    def __init__(self, db_path: str = 'orders.db'):
        """Initialize the database connection and create tables if they don't exist."""
        self.db_path = db_path
        self.create_tables()

    def get_connection(self):
        """Get a connection to the SQLite database."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Return rows as dictionaries
        return conn

    def create_tables(self):
        """Create the necessary tables if they don't exist."""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Create orders table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    number TEXT NOT NULL UNIQUE,
                    details TEXT NOT NULL,
                    stage TEXT NOT NULL DEFAULT 'investigation',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)

            # Create order_logs table to track order history
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS order_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id INTEGER NOT NULL,
                    action TEXT NOT NULL,
                    notes TEXT,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (order_id) REFERENCES orders (id)
                )
            """)

            conn.commit()

    def add_order(self, number: str, details: str, stage: str = 'investigation'):
        """Add a new order to the database.

        Args:
            number: The order number
            details: Order details
            stage: Initial stage (default: 'investigation')

        Returns:
            The ID of the newly created order
        """
        now = datetime.datetime.now().isoformat()

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Insert the order
            cursor.execute("""
                INSERT INTO orders (number, details, stage, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            """, (number, details, stage, now, now))

            order_id = cursor.lastrowid

            # Log the creation
            cursor.execute("""
                INSERT INTO order_logs (order_id, action, created_at)
                VALUES (?, ?, ?)
            """, (order_id, 'تم إنشاء الأمر', now))

            conn.commit()

            return order_id

    def get_order(self, order_id: int):
        """Get a single order by ID.

        Args:
            order_id: The ID of the order to retrieve

        Returns:
            A dictionary containing the order data, or None if not found
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM orders WHERE id = ?
            """, (order_id,))

            order = cursor.fetchone()

            if order:
                return dict(order)
            return None

    def get_orders(self, stage: str = None, number_search: str = None):
        """Get all orders, optionally filtered by stage and/or number.

        Args:
            stage: Filter by stage (optional)
            number_search: Search in order number (optional)

        Returns:
            A list of dictionaries containing order data
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            query = 'SELECT * FROM orders'
            params = []

            conditions = []
            if stage:
                conditions.append('stage = ?')
                params.append(stage)

            if number_search:
                conditions.append('number LIKE ?')
                params.append(f'%{number_search}%')

            if conditions:
                query += ' WHERE ' + ' AND '.join(conditions)

            query += ' ORDER BY updated_at DESC'

            cursor.execute(query, params)

            return [dict(order) for order in cursor.fetchall()]

    def update_order_stage(self, order_id: int, new_stage: str, notes: str = None):
        """Update the stage of an order.

        Args:
            order_id: The ID of the order to update
            new_stage: The new stage
            notes: Optional notes for the log

        Returns:
            True if successful, False otherwise
        """
        now = datetime.datetime.now().isoformat()

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Update the order
            cursor.execute("""
                UPDATE orders 
                SET stage = ?, updated_at = ?
                WHERE id = ?
            """, (new_stage, now, order_id))

            if cursor.rowcount == 0:
                return False

            # Log the change
            action = f'تم تغيير المرحلة إلى {self._get_stage_text(new_stage)}'
            cursor.execute("""
                INSERT INTO order_logs (order_id, action, notes, created_at)
                VALUES (?, ?, ?, ?)
            """, (order_id, action, notes, now))

            conn.commit()

            return True

    def get_order_logs(self, order_id: int):
        """Get the log history for an order.

        Args:
            order_id: The ID of the order

        Returns:
            A list of dictionaries containing log data
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM order_logs 
                WHERE order_id = ?
                ORDER BY created_at DESC
            """, (order_id,))

            return [dict(log) for log in cursor.fetchall()]

    def get_latest_log(self, order_id: int):
        """Get the latest log entry for an order.

        Args:
            order_id: The ID of the order

        Returns:
            A dictionary containing the latest log data, or None if not found
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM order_logs 
                WHERE order_id = ?
                ORDER BY created_at DESC
                LIMIT 1
            """, (order_id,))

            log = cursor.fetchone()

            if log:
                return dict(log)
            return None

    def bulk_update_stage(self, order_ids: List[int], new_stage: str, notes: str = None):
        """Update the stage of multiple orders.

        Args:
            order_ids: List of order IDs to update
            new_stage: The new stage
            notes: Optional notes for the log

        Returns:
            The number of orders updated
        """
        now = datetime.datetime.now().isoformat()
        updated_count = 0

        with self.get_connection() as conn:
            cursor = conn.cursor()

            for order_id in order_ids:
                # Update the order
                cursor.execute("""
                    UPDATE orders 
                    SET stage = ?, updated_at = ?
                    WHERE id = ?
                """, (new_stage, now, order_id))

                if cursor.rowcount > 0:
                    updated_count += 1

                    # Log the change
                    action = f'تم تغيير المرحلة إلى {self._get_stage_text(new_stage)}'
                    cursor.execute("""
                        INSERT INTO order_logs (order_id, action, notes, created_at)
                        VALUES (?, ?, ?, ?)
                    """, (order_id, action, notes, now))

            conn.commit()

            return updated_count

    def _get_stage_text(self, stage: str):
        """Get the Arabic text for a stage.

        Args:
            stage: The stage code

        Returns:
            The Arabic text for the stage
        """
        stages = {
            'investigation': 'التحقيق (المحاسبة)',
            'audit': 'التدقيق (قسم التدقيق)',
            'entry': 'الإدخال (قسم التأدية والرواتب)'
        }

        return stages.get(stage, stage)


# Create a singleton instance for easy import
db = OrderDatabase()
